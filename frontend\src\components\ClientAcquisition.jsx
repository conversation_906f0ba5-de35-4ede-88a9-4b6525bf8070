import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import QuestionnaireLoader from './QuestionnaireLoader';
import AIAgent from './AIAgent';
import QuestionnaireButtonGrid from './questionnaire/QuestionnaireButtonGrid';
import QuestionnaireCompletionStatus from './questionnaire/QuestionnaireCompletionStatus';
import StrategyGenerationSection from './questionnaire/StrategyGenerationSection';
import { getQuestionnaireResponses } from '../supabase/client';

function ClientAcquisition() {
  const navigate = useNavigate();
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    leadGeneration: false,
    customerAcquisitionStrategy: false,
    marketingCampaigns: false,
    customerOnboarding: false
  });

  const [loading, setLoading] = useState(true);

  const questionnaireConfigs = {
    leadGeneration: {
      title: 'Lead Generation Questionnaire',
      description: 'Optimize your lead generation strategies to attract and convert potential customers.',
      files: ['lead-generation_strategy-questionnaire-01.yaml'],
      defaultFile: 'lead-generation_strategy-questionnaire-01.yaml'
    },
    customerAcquisitionStrategy: {
      title: 'Customer Acquisition Strategy Questionnaire',
      description: 'Develop comprehensive strategies to acquire new customers effectively.',
      files: ['customer-acquisition-strategy-questionnaire.yaml'],
      defaultFile: 'customer-acquisition-strategy-questionnaire.yaml'
    },
    marketingCampaigns: {
      title: 'Marketing Campaigns Questionnaire',
      description: 'Create and optimize marketing campaigns that drive customer acquisition.',
      files: ['marketing-campaigns-design-questionnaire.yaml'],
      defaultFile: 'marketing-campaigns-design-questionnaire.yaml'
    },
    customerOnboarding: {
      title: 'Customer Onboarding Questionnaire',
      description: 'Assess and improve your customer onboarding process to increase retention and customer lifetime value.',
      files: ['customer-onboarding-improvement-questionnaire.yaml'],
      defaultFile: 'customer-onboarding-improvement-questionnaire.yaml'
    }
  };

  const [actualCompletedQuestionnaires, setActualCompletedQuestionnaires] = useState({
    leadGeneration: false,
    customerAcquisitionStrategy: false,
    marketingCampaigns: false,
    customerOnboarding: false
  });

  useEffect(() => {
    const checkCompletedQuestionnaires = async () => {
      try {
        setLoading(true);
        const completed = {};
        for (const key of Object.keys(questionnaireConfigs)) {
          try {
            const { data, error } = await getQuestionnaireResponses(key);
            completed[key] = !error && data && Object.keys(data.responses || {}).length > 0;
          } catch (err) {
            const storedData = localStorage.getItem(`questionnaire_responses_${key}`);
            completed[key] = !!storedData;
          }
        }
        setActualCompletedQuestionnaires(completed);
        setCompletedQuestionnaires(completed);
      } catch (error) {
        console.error('Error checking completed questionnaires:', error);
        // Initialize with default empty state
        setActualCompletedQuestionnaires({
          leadGeneration: false,
          customerAcquisitionStrategy: false,
          marketingCampaigns: false,
          customerOnboarding: false
        });
      } finally {
        setLoading(false);
      }
    };

    checkCompletedQuestionnaires();
  }, []);

  const handleSubmit = async (data) => {
    try {
      setSubmissionResult({
        success: true,
        message: 'Questionnaire submitted successfully!',
        data: data
      });

      const updatedCompleted = { ...completedQuestionnaires };
      if (activeQuestionnaire) {
        updatedCompleted[activeQuestionnaire] = true;
      }
      setCompletedQuestionnaires(updatedCompleted);
      setActualCompletedQuestionnaires(updatedCompleted);
      setActiveQuestionnaire(null);
    } catch (error) {
      console.error('Error submitting questionnaire:', error);
      setSubmissionResult({
        success: false,
        message: 'Failed to submit questionnaire. Please try again.',
        data: null
      });
    }
  };

  const handleGenerateStrategy = async (data) => {
    setGeneratingStrategy(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 3000));
      setSubmissionResult({
        success: true,
        message: 'Strategy generated successfully!',
        data: data,
        isStrategy: true
      });
      setActiveQuestionnaire(null);
    } catch (error) {
      console.error('Error generating strategy:', error);
      setSubmissionResult({
        success: false,
        message: 'Failed to generate strategy. Please try again.',
        data: null
      });
    } finally {
      setGeneratingStrategy(false);
    }
  };

  // Toggle questionnaire visibility
  const toggleQuestionnaire = (questionnaireType) => {
    console.log('Toggling questionnaire:', questionnaireType, 'Current active:', activeQuestionnaire);
    setActiveQuestionnaire(activeQuestionnaire === questionnaireType ? null : questionnaireType);
  };



  // Questionnaire data for the button grid
  const questionnaireData = [
    {
      key: 'leadGeneration',
      title: 'Lead Generation',
      description: 'Optimize your lead generation strategies to attract and convert potential customers.'
    },
    {
      key: 'customerAcquisitionStrategy',
      title: 'Customer Acquisition Strategy',
      description: 'Develop comprehensive strategies to acquire new customers effectively.'
    },
    {
      key: 'marketingCampaigns',
      title: 'Marketing Campaigns',
      description: 'Create and optimize marketing campaigns that drive customer acquisition.'
    },
    {
      key: 'customerOnboarding',
      title: 'Customer Onboarding',
      description: 'Assess and improve your customer onboarding process to increase retention and customer lifetime value.'
    }
  ];

  // Questionnaire data for completion status
  const statusQuestionnaires = [
    { key: 'leadGeneration', displayName: 'Lead Generation' },
    { key: 'customerAcquisitionStrategy', displayName: 'Customer Acquisition Strategy' },
    { key: 'marketingCampaigns', displayName: 'Marketing Campaigns' },
    { key: 'customerOnboarding', displayName: 'Customer Onboarding' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="w-full max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <h1 className="raleway-title-h1 mb-6 text-center text-amber-800">CLIENT ACQUISITION</h1>
            <p className="body-text text-center mb-8 max-w-3xl mx-auto">
              Develop comprehensive strategies to attract, convert, and onboard new customers. 
              Our questionnaires help you optimize your client acquisition process from lead generation to customer success.
            </p>

            <QuestionnaireButtonGrid
              questionnaires={questionnaireData}
              activeQuestionnaire={activeQuestionnaire}
              onQuestionnaireToggle={toggleQuestionnaire}
              themeColor="amber"
              columns={2}
            />

            <QuestionnaireCompletionStatus
              questionnaires={statusQuestionnaires}
              completionStatus={actualCompletedQuestionnaires}
              loading={loading}
              title="Questionnaire Completion Status"
              columns={2}
            />

            <StrategyGenerationSection
              title="GENERATE CLIENT ACQUISITION STRATEGY"
              description="Ready to turn your questionnaire responses into an actionable client acquisition strategy? Click the button below to generate a comprehensive strategy tailored to your business needs."
              buttonText="Generate Strategy"
              strategyType="Client Acquisition Questionnaires"
              completionStatus={completedQuestionnaires}
              themeColor="amber"
              navigateTo="/strategy"
            />
          </div>
        </div>

        {/* Questionnaire Section */}
        {activeQuestionnaire && (
          <div className="mb-8 p-6 bg-white rounded-lg border border-gray-200 shadow-md">
            <div className="flex justify-end mb-4">
              <button
                onClick={() => setActiveQuestionnaire(null)}
                className="text-gray-500 hover:text-gray-700"
                aria-label="Close questionnaire"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <QuestionnaireLoader
              key={activeQuestionnaire}
              title={questionnaireConfigs[activeQuestionnaire].title}
              description={questionnaireConfigs[activeQuestionnaire].description}
              specificQuestionnaires={questionnaireConfigs[activeQuestionnaire].files}
              defaultQuestionnaire={questionnaireConfigs[activeQuestionnaire].defaultFile}
              onSubmit={handleSubmit}
              onGenerateStrategy={handleGenerateStrategy}
              showLocalSave={false}
              hideGenerateStrategyButton={true}
              hideQuestionnaireSelector={true}
            />
          </div>
        )}

        {/* Questionnaire Success Message Popup */}
        {submissionResult && (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="absolute inset-0 bg-black opacity-30"></div>
            <div className="bg-white p-6 rounded-lg shadow-xl border border-green-200 z-10 max-w-md w-full mx-4">
              <div className="flex items-center justify-center mb-4 text-green-500">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h2 className="text-xl font-semibold mb-2 text-center text-green-700">
                {submissionResult.message}
              </h2>
              <button
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition w-full"
                onClick={() => setSubmissionResult(null)}
              >
                Close
              </button>
            </div>
          </div>
        )}

        {/* Strategy Generation Loading */}
        {generatingStrategy && (
          <div className="bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
            <div className="animate-pulse flex flex-col items-center">
              <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
              <p className="body-text">We're analyzing your responses and creating a customized client acquisition strategy...</p>
            </div>
          </div>
        )}

        {/* Client Acquisition AI Agent */}
        <AIAgent
          pageId="client-acquisition"
        />
      </div>
    </div>
  );
}

export default ClientAcquisition;
