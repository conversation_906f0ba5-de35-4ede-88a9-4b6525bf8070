import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AIAgent from './AIAgent';
import QuestionnaireLoader from './QuestionnaireLoader';
import QuestionnaireButtonGrid from './questionnaire/QuestionnaireButtonGrid';
import QuestionnaireCompletionStatus from './questionnaire/QuestionnaireCompletionStatus';
import StrategyGenerationSection from './questionnaire/StrategyGenerationSection';
import { saveQuestionnaireResponses, getAllCompletedQuestionnaires, getQuestionnaireResponses } from '../supabase/client';
import { useAuth } from '../context/AuthContext';

function CustomerRetention() {
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Track which questionnaires have been completed
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    loyaltyPrograms: false,
    customerFeedback: false,
    personalization: false,
    customerSupport: false
  });

  // Track which questionnaires have actual responses saved
  const [actualCompletedQuestionnaires, setActualCompletedQuestionnaires] = useState({
    loyaltyPrograms: false,
    customerFeedback: false,
    personalization: false,
    customerSupport: false
  });

  // Loading state for initial data fetch
  const [loading, setLoading] = useState(true);

  // Questionnaire configurations
  const questionnaireConfigs = {
    loyaltyPrograms: {
      title: "Loyalty Programs Design & Optimization Questionnaire",
      description: "",
      files: ['loyalty-programs-design-optimization-questionnaire.yaml'],
      defaultFile: 'loyalty-programs-design-optimization-questionnaire.yaml'
    },
    customerFeedback: {
      title: "Customer Feedback Strategy Questionnaire",
      description: "",
      files: ['customer-feedback-strategy-questionnaire.yaml'],
      defaultFile: 'customer-feedback-strategy-questionnaire.yaml'
    },
    personalization: {
      title: "Personalization Strategy Questionnaire",
      description: "",
      files: ['personalization-strategy-questionnaire.yaml'],
      defaultFile: 'personalization-strategy-questionnaire.yaml'
    },
    customerSupport: {
      title: "Customer Support Enhancement Questionnaire",
      description: "",
      files: ['customer-support-enhancement-questionnaire.yaml'],
      defaultFile: 'customer-support-enhancement-questionnaire.yaml'
    }
  };

  // Load completed questionnaires on component mount
  useEffect(() => {
    const loadCompletedQuestionnaires = async () => {
      if (!user) {
        setLoading(false);
        return;
      }

      try {
        const completed = await getAllCompletedQuestionnaires(user.id);
        console.log('Completed questionnaires:', completed);

        // Update completion status based on actual saved responses
        const newCompletedStatus = { ...completedQuestionnaires };
        const newActualCompletedStatus = { ...actualCompletedQuestionnaires };

        Object.keys(questionnaireConfigs).forEach(key => {
          const config = questionnaireConfigs[key];
          const hasCompleted = completed.some(q =>
            config.files.includes(q.questionnaire_name)
          );
          newCompletedStatus[key] = hasCompleted;
          newActualCompletedStatus[key] = hasCompleted;
        });

        setCompletedQuestionnaires(newCompletedStatus);
        setActualCompletedQuestionnaires(newActualCompletedStatus);
      } catch (error) {
        console.error('Error loading completed questionnaires:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCompletedQuestionnaires();
  }, [user]);

  // Toggle questionnaire visibility
  const toggleQuestionnaire = (questionnaireKey) => {
    if (activeQuestionnaire === questionnaireKey) {
      setActiveQuestionnaire(null);
    } else {
      setActiveQuestionnaire(questionnaireKey);
    }
  };

  // Handle questionnaire submission
  const handleSubmit = async (data) => {
    console.log('Questionnaire submitted:', data);

    try {
      if (user) {
        await saveQuestionnaireResponses(user.id, data.questionnaire, data.responses);
        console.log('Responses saved to Supabase');

        // Update completion status
        const questionnaireKey = Object.keys(questionnaireConfigs).find(key =>
          questionnaireConfigs[key].files.includes(data.questionnaire)
        );

        if (questionnaireKey) {
          setCompletedQuestionnaires(prev => ({ ...prev, [questionnaireKey]: true }));
          setActualCompletedQuestionnaires(prev => ({ ...prev, [questionnaireKey]: true }));
        }
      }

      setSubmissionResult({
        success: true,
        message: 'Thank you for your submission!',
        data: data
      });
    } catch (error) {
      console.error('Error saving questionnaire responses:', error);
      setSubmissionResult({
        success: false,
        message: 'There was an error saving your responses. Please try again.',
        data: data
      });
    }
  };

  // Handle strategy generation
  const handleGenerateStrategy = (data) => {
    console.log('Generating strategy from data:', data);
    setGeneratingStrategy(true);

    // Simulate API call to generate strategy
    setTimeout(() => {
      setSubmissionResult({
        success: true,
        message: 'Your customer retention strategy has been generated!',
        data: data,
        isStrategy: true
      });
      setGeneratingStrategy(false);
    }, 2000);
  };



  // Questionnaire data for the button grid
  const questionnaireData = [
    {
      key: 'loyaltyPrograms',
      title: 'Loyalty Programs',
      description: 'Design or optimize loyalty programs to effectively reward and retain your valued customers.'
    },
    {
      key: 'customerFeedback',
      title: 'Customer Feedback',
      description: 'Develop and optimize your customer feedback mechanisms to drive continuous improvement in your products and services.'
    },
    {
      key: 'personalization',
      title: 'Personalization',
      description: 'Develop and refine your personalization strategy to create stronger customer relationships and drive business impact.'
    },
    {
      key: 'customerSupport',
      title: 'Customer Support',
      description: 'Assess and enhance your customer support operations to build trust and improve customer satisfaction.'
    }
  ];

  // Questionnaire data for completion status
  const statusQuestionnaires = [
    { key: 'loyaltyPrograms', displayName: 'Loyalty Programs' },
    { key: 'customerFeedback', displayName: 'Customer Feedback' },
    { key: 'personalization', displayName: 'Personalization' },
    { key: 'customerSupport', displayName: 'Customer Support' }
  ];

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-2/3 mx-auto mb-4"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              {[1, 2, 3, 4].map(i => (
                <div key={i} className="h-24 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="w-full max-w-4xl p-6 bg-white rounded shadow-md text-center mx-auto">
        <h2 className="raleway-title-h2 mb-4">Customer Retention</h2>
        <p className="body-text mb-4">
          Strategies and tools to increase customer loyalty and reduce churn rate.
        </p>

        <QuestionnaireButtonGrid
          questionnaires={questionnaireData}
          activeQuestionnaire={activeQuestionnaire}
          onQuestionnaireToggle={toggleQuestionnaire}
          themeColor="purple"
          columns={2}
        />

        <QuestionnaireCompletionStatus
          questionnaires={statusQuestionnaires}
          completionStatus={actualCompletedQuestionnaires}
          loading={loading}
          title="Questionnaire Completion Status"
          columns={2}
        />

        <StrategyGenerationSection
          title="GENERATE CUSTOMER RETENTION STRATEGY"
          description="Ready to turn your questionnaire responses into an actionable customer retention strategy? Click the button below to generate a comprehensive strategy tailored to your business needs."
          buttonText="Generate Strategy"
          strategyType="Customer Retention Questionnaires"
          completionStatus={completedQuestionnaires}
          themeColor="purple"
          navigateTo="/strategy"
        />
      </div>

      {/* Active Questionnaire */}
      {activeQuestionnaire && (
        <div className="w-full max-w-4xl mx-auto p-6 bg-white rounded shadow-md">
          <div className="mb-4 flex justify-end items-center">
            <button
              onClick={() => setActiveQuestionnaire(null)}
              className="text-gray-500 hover:text-gray-700 text-xl font-bold"
            >
              ×
            </button>
          </div>

          <QuestionnaireLoader
            specificQuestionnaires={questionnaireConfigs[activeQuestionnaire].files}
            defaultQuestionnaire={questionnaireConfigs[activeQuestionnaire].defaultFile}
            onSubmit={handleSubmit}
            onGenerateStrategy={handleGenerateStrategy}
            showLocalSave={true}
            hideGenerateStrategyButton={true}
            hideQuestionnaireSelector={true}
          />
        </div>
      )}

      {/* Questionnaire Success Message */}
      {submissionResult && (
        <div className={`w-full max-w-4xl mx-auto p-6 rounded-lg shadow-md border mb-8 ${
          submissionResult.success
            ? 'bg-green-50 border-green-200'
            : 'bg-red-50 border-red-200'
        }`}>
          <h2 className={`text-xl font-semibold mb-4 ${
            submissionResult.success ? 'text-green-700' : 'text-red-700'
          }`}>
            <svg className="inline-block w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={
                submissionResult.success ? "M5 13l4 4L19 7" : "M6 18L18 6M6 6l12 12"
              }></path>
            </svg>
            {submissionResult.message}
          </h2>
          {submissionResult.success && submissionResult.isStrategy ? (
            <div>
              <p className="mb-4">
                Based on your responses to the "{submissionResult.data.questionnaire}" questionnaire,
                we've generated a customized customer retention strategy for your business.
              </p>
              <div className="p-4 bg-white rounded border border-blue-100 mb-4">
                <h3 className="raleway-title-h3 mb-2">Your Customer Retention Strategy</h3>
                <p className="body-text mb-2">This strategy is tailored to your specific business needs:</p>
                <ul className="list-disc pl-5 space-y-2 body-text">
                  <li>Implement a loyalty program that rewards repeat customers with exclusive discounts</li>
                  <li>Create a personalized email campaign to re-engage customers who haven't purchased in 3+ months</li>
                  <li>Develop a customer feedback system to identify and address pain points</li>
                  <li>Offer specialized customer service training to your team to improve satisfaction</li>
                  <li>Establish a referral program to incentivize existing customers to bring in new business</li>
                </ul>
              </div>
            </div>
          ) : submissionResult.success ? (
            <p className="mb-4">
              We've received your responses for the "{submissionResult.data.questionnaire}" questionnaire.
            </p>
          ) : null}
          <div className="flex justify-center">
            <button
              className={`mt-2 px-4 py-2 text-white rounded hover:opacity-90 transition ${
                submissionResult.success ? 'bg-blue-600' : 'bg-red-600'
              }`}
              onClick={() => setSubmissionResult(null)}
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Strategy Generation Loading */}
      {generatingStrategy && (
        <div className="w-full max-w-4xl mx-auto bg-blue-50 p-6 rounded-lg shadow-md border border-blue-200 mb-8 text-center">
          <div className="animate-pulse flex flex-col items-center">
            <svg className="animate-spin h-10 w-10 text-blue-600 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <h2 className="text-xl font-semibold mb-2 text-blue-700">Generating Your Strategy</h2>
            <p className="body-text">We're analyzing your responses and creating a customized customer retention strategy...</p>
          </div>
        </div>
      )}

      {/* Customer Retention AI Agent */}
      <div className="w-full max-w-4xl mx-auto">
        <AIAgent
          pageId="customer-retention"
          themeColor="purple"
        />
      </div>
    </div>
  );
}

export default CustomerRetention;
