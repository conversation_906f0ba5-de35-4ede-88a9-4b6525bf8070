import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import AIAgent from './AIAgent';
import QuestionnaireLoader from './QuestionnaireLoader';
import QuestionnaireButtonGrid from './questionnaire/QuestionnaireButtonGrid';
import QuestionnaireCompletionStatus from './questionnaire/QuestionnaireCompletionStatus';
import StrategyGenerationSection from './questionnaire/StrategyGenerationSection';
import WhyPartnerSection from './partnerships/WhyPartnerSection';
import PartnershipJourneySection from './partnerships/PartnershipJourneySection';
import { saveQuestionnaireResponses, getAllCompletedQuestionnaires, getQuestionnaireResponses } from '../supabase/client';
import { useAuth } from '../context/AuthContext';

function Partnerships() {
  const [activeQuestionnaire, setActiveQuestionnaire] = useState(null);
  const [submissionResult, setSubmissionResult] = useState(null);
  const [generatingStrategy, setGeneratingStrategy] = useState(false);
  const navigate = useNavigate();
  const { user } = useAuth();

  // Track which questionnaires have been completed
  const [completedQuestionnaires, setCompletedQuestionnaires] = useState({
    partnershipStrategy: false,
    partnershipPrograms: false,
    partnershipJourney: false,
    partnerSuccess: false
  });

  const [loading, setLoading] = useState(true);

  // Load completion status from Supabase
  useEffect(() => {
    const loadCompletionStatus = async () => {
      if (!user) return;
      
      try {
        setLoading(true);
        const completed = await getAllCompletedQuestionnaires(user.id);
        
        const partnershipCompletions = {
          partnershipStrategy: completed.some(q => q.questionnaire_type === 'partnership-strategy-questionnaire'),
          partnershipPrograms: completed.some(q => q.questionnaire_type === 'partnership-programs-questionnaire'),
          partnershipJourney: completed.some(q => q.questionnaire_type === 'partnership-journey-questionnaire'),
          partnerSuccess: completed.some(q => q.questionnaire_type === 'partner-success-questionnaire')
        };
        
        setCompletedQuestionnaires(partnershipCompletions);
      } catch (error) {
        console.error('Error loading completion status:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCompletionStatus();
  }, [user]);

  const toggleQuestionnaire = (questionnaireKey) => {
    setActiveQuestionnaire(activeQuestionnaire === questionnaireKey ? null : questionnaireKey);
  };

  const handleQuestionnaireSubmit = async (responses) => {
    if (!user) return;

    try {
      const questionnaireTypeMap = {
        partnershipStrategy: 'partnership-strategy-questionnaire',
        partnershipPrograms: 'partnership-programs-questionnaire',
        partnershipJourney: 'partnership-journey-questionnaire',
        partnerSuccess: 'partner-success-questionnaire'
      };

      const questionnaireType = questionnaireTypeMap[activeQuestionnaire];
      
      await saveQuestionnaireResponses(user.id, questionnaireType, responses);
      
      setSubmissionResult({
        success: true,
        message: 'Partnership questionnaire responses saved successfully!'
      });

      // Update completion status
      setCompletedQuestionnaires(prev => ({
        ...prev,
        [activeQuestionnaire]: true
      }));

      // Close the questionnaire
      setActiveQuestionnaire(null);
    } catch (error) {
      console.error('Error saving responses:', error);
      setSubmissionResult({
        success: false,
        message: 'Failed to save responses. Please try again.'
      });
    }
  };

  // Questionnaire data for the button grid
  const questionnaireData = [
    {
      key: 'partnershipStrategy',
      title: 'Strategy',
      description: 'Define your overall partnership approach and strategic objectives for business growth.'
    },
    {
      key: 'partnershipPrograms',
      title: 'Programs',
      description: 'Define and articulate the different types of partnership programs you offer, including their focus, ideal partner profiles, and unique benefits.'
    },
    {
      key: 'partnershipJourney',
      title: 'Journey',
      description: 'Define and streamline the step-by-step process for potential partners to engage with and join your ecosystem, setting clear expectations and presenting a transparent path to collaboration.'
    },
    {
      key: 'partnerSuccess',
      title: 'Success & Resources',
      description: 'Articulate the comprehensive support, tools, and benefits your platform provides to partners, ensuring they have everything needed to succeed and grow within your ecosystem.'
    }
  ];

  // Status questionnaires for completion tracking
  const statusQuestionnaires = [
    { key: 'partnershipStrategy', displayName: 'Partnership Strategy' },
    { key: 'partnershipPrograms', displayName: 'Partnership Programs' },
    { key: 'partnershipJourney', displayName: 'Partnership Journey' },
    { key: 'partnerSuccess', displayName: 'Partner Success & Resources' }
  ];

  // Get actual completion status (handle both old localStorage and new Supabase data)
  const actualCompletedQuestionnaires = completedQuestionnaires;

  // Questionnaire configurations
  const questionnaireConfigs = {
    partnershipStrategy: {
      title: "Partnership Strategy Questionnaire",
      description: "Define your partnership approach and strategic objectives.",
      files: ['partnership-strategy-questionnaire.yaml'],
      defaultFile: 'partnership-strategy-questionnaire.yaml'
    },
    partnershipPrograms: {
      title: "Partnership Programs Questionnaire",
      description: "Define and articulate the different types of partnership programs you offer.",
      files: ['partnership-programs-questionnaire.yaml'],
      defaultFile: 'partnership-programs-questionnaire.yaml'
    },
    partnershipJourney: {
      title: "Partnership Journey Questionnaire",
      description: "Define and streamline the step-by-step process for potential partners to engage with your ecosystem.",
      files: ['partnership-journey-questionnaire.yaml'],
      defaultFile: 'partnership-journey-questionnaire.yaml'
    },
    partnerSuccess: {
      title: "Partner Success & Resources Questionnaire",
      description: "Articulate the comprehensive support, tools, and benefits your platform provides to partners.",
      files: ['partner-success-questionnaire.yaml'],
      defaultFile: 'partner-success-questionnaire.yaml'
    }
  };



  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="w-full max-w-6xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-8 mb-8">
            <h1 className="raleway-title-h1 mb-6 text-center text-purple-800">PARTNERSHIPS</h1>
            <p className="body-text text-center mb-8 max-w-3xl mx-auto">
              Build strategic partnerships that drive mutual growth and create lasting value. 
              Our comprehensive partnership framework helps you identify, develop, and manage successful business alliances.
            </p>

            {/* Section 1: Why Partner With Us? */}
            <WhyPartnerSection
              title="Why Partner With Us?"
              description="Partnering with our market intelligence platform opens doors to unprecedented opportunities for growth and innovation. We offer compelling value propositions that benefit both parties through:"
              themeColor="purple"
            />

            {/* Section 3: The Partnership Journey */}
            <PartnershipJourneySection
              title="The Partnership Journey"
              description="Our structured approach ensures successful partnership development from initial contact to active collaboration:"
              themeColor="purple"
            />

            <QuestionnaireButtonGrid
              questionnaires={questionnaireData}
              activeQuestionnaire={activeQuestionnaire}
              onQuestionnaireToggle={toggleQuestionnaire}
              themeColor="purple"
              columns={2}
            />

            <QuestionnaireCompletionStatus
              questionnaires={statusQuestionnaires}
              completionStatus={actualCompletedQuestionnaires}
              loading={loading}
              title="Questionnaire Completion Status"
              columns={2}
            />

            <StrategyGenerationSection
              title="GENERATE PARTNERSHIP STRATEGY"
              description="Ready to turn your questionnaire responses into an actionable partnership strategy? Click the button below to generate a comprehensive strategy tailored to your business needs."
              buttonText="Generate Strategy"
              strategyType="Partnership Questionnaires"
              completionStatus={completedQuestionnaires}
              themeColor="purple"
              navigateTo="/strategy"
            />
          </div>
        </div>

        {/* Questionnaire Section */}
        {activeQuestionnaire && (
          <div className="w-full max-w-6xl mx-auto mb-8">
            <div className="bg-white rounded-lg border border-gray-200 shadow-md p-6">
              <div className="flex justify-end mb-4">
                <button
                  onClick={() => setActiveQuestionnaire(null)}
                  className="text-gray-500 hover:text-gray-700"
                  aria-label="Close questionnaire"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <QuestionnaireLoader
                key={activeQuestionnaire}
                title={questionnaireConfigs[activeQuestionnaire].title}
                description={questionnaireConfigs[activeQuestionnaire].description}
                specificQuestionnaires={questionnaireConfigs[activeQuestionnaire].files}
                defaultQuestionnaire={questionnaireConfigs[activeQuestionnaire].defaultFile}
                onSubmit={handleQuestionnaireSubmit}
                submissionResult={submissionResult}
                hideQuestionnaireSelector={true}
                hideGenerateStrategyButton={true}
              />
            </div>
          </div>
        )}

        {/* Loading State */}
        {generatingStrategy && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-8 rounded-lg shadow-lg max-w-md w-full mx-4">
              <div className="flex items-center justify-center mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              </div>
              <h3 className="raleway-title-h3 mb-2 text-center">Generating Partnership Strategy</h3>
              <p className="body-text">We're analyzing your responses and creating a customized partnership strategy...</p>
            </div>
          </div>
        )}

        {/* Partnership AI Agent */}
        <AIAgent
          pageId="partnerships"
        />
      </div>
    </div>
  );
}

export default Partnerships;
