import React from 'react';
import { useNavigate } from 'react-router-dom';

/**
 * StrategyGenerationSection Component
 * 
 * A reusable component that provides strategy generation functionality
 * with consistent styling and behavior across different questionnaire pages.
 * 
 * @param {Object} props - Component props
 * @param {string} props.title - Title for the strategy generation section
 * @param {string} props.description - Description text explaining the strategy generation
 * @param {string} props.buttonText - Text for the generate strategy button
 * @param {string} props.strategyType - Type of strategy being generated (for sessionStorage)
 * @param {Object} props.completionStatus - Object mapping questionnaire keys to completion status
 * @param {string} props.themeColor - Theme color for styling (e.g., 'red', 'amber', 'blue')
 * @param {Function} props.onGenerateStrategy - Optional custom callback for strategy generation
 * @param {boolean} props.disabled - Whether the button should be disabled
 * @param {string} props.navigateTo - Route to navigate to (default: '/strategy')
 */
function StrategyGenerationSection({
  title = "GENERATE STRATEGY",
  description = "Ready to turn your questionnaire responses into an actionable strategy? Click the button below to generate a comprehensive strategy tailored to your business needs.",
  buttonText = "Generate Strategy",
  strategyType = "Strategy",
  completionStatus = {},
  themeColor = 'red',
  onGenerateStrategy = null,
  disabled = false,
  navigateTo = '/strategy'
}) {
  const navigate = useNavigate();

  // Define color schemes for different themes
  const colorSchemes = {
    red: {
      border: 'border-red-200',
      titleText: 'text-red-800'
    },
    amber: {
      border: 'border-amber-200',
      titleText: 'text-amber-800'
    },
    blue: {
      border: 'border-blue-200',
      titleText: 'text-blue-800'
    },
    green: {
      border: 'border-green-200',
      titleText: 'text-green-800'
    },
    purple: {
      border: 'border-purple-200',
      titleText: 'text-purple-800'
    }
  };

  const colors = colorSchemes[themeColor] || colorSchemes.red;

  // Check if any questionnaires are completed
  const hasCompletedQuestionnaires = Object.values(completionStatus).some(value => value);
  const isDisabled = disabled || !hasCompletedQuestionnaires;

  const handleGenerateStrategy = () => {
    if (onGenerateStrategy) {
      // Use custom callback if provided
      onGenerateStrategy(completionStatus);
    } else {
      // Default behavior: store responses and navigate
      const responseData = {
        questionnaire: strategyType,
        responses: completionStatus,
        timestamp: new Date().toISOString()
      };
      sessionStorage.setItem('questionnaire_responses', JSON.stringify(responseData));
      navigate(navigateTo);
    }
  };

  return (
    <div className={`mt-8 p-6 bg-white rounded-lg border ${colors.border} shadow-md`}>
      <h4 className={`raleway-title-h4 mb-3 ${colors.titleText}`}>
        {title}
      </h4>
      <p className="body-text mb-4">
        {description}
      </p>
      <div className="flex flex-wrap gap-4">
        <button
          className={`px-6 py-2 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition flex items-center ${
            isDisabled ? 'opacity-50 cursor-not-allowed' : ''
          }`}
          onClick={handleGenerateStrategy}
          disabled={isDisabled}
        >
          <svg 
            className="w-5 h-5 mr-2" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24" 
            xmlns="http://www.w3.org/2000/svg"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth="2" 
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
            />
          </svg>
          {buttonText}
        </button>
      </div>
    </div>
  );
}

export default StrategyGenerationSection;
