import React, { createContext, useState, useEffect, useContext, useRef } from 'react';
import { supabase } from '../supabase/client';
import { UserProfileService } from '../services/userProfileService';
import { AuthDebugger } from '../utils/authDebugger';

// Storage cleanup utility
const StorageManager = {
  // Current storage version - increment when making breaking changes
  STORAGE_VERSION: '1.1.0', // Incremented to force cleanup

  // Clear potentially conflicting cached data
  clearUserSpecificData() {
    console.log('🧹 Clearing user-specific cached data...');

    // Clear user-specific localStorage items
    const keysToRemove = [
      'companyProfile',
      'completedQuestionnaires',
      'savedResponses'
    ];

    keysToRemove.forEach(key => {
      if (localStorage.getItem(key)) {
        console.log(`Removing localStorage item: ${key}`);
        localStorage.removeItem(key);
      }
    });

    // Clear questionnaire response items (they follow a pattern)
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith('questionnaire_responses_')) {
        console.log(`Removing localStorage item: ${key}`);
        localStorage.removeItem(key);
      }
    });

    // Clear sessionStorage items
    const sessionKeysToRemove = [
      'questionnaire_view_responses',
      'generated_strategy'
    ];

    sessionKeysToRemove.forEach(key => {
      if (sessionStorage.getItem(key)) {
        console.log(`Removing sessionStorage item: ${key}`);
        sessionStorage.removeItem(key);
      }
    });
  },

  // Check and handle storage version changes
  checkStorageVersion() {
    try {
      const storedVersion = localStorage.getItem('storage_version');
      if (storedVersion !== this.STORAGE_VERSION) {
        AuthDebugger.log('STORAGE', 'Storage version mismatch, clearing old data', {
          oldVersion: storedVersion,
          newVersion: this.STORAGE_VERSION
        });

        // Clear potentially conflicting data
        this.clearUserSpecificData();
        this.clearSupabaseAuth();

        // Set new version
        localStorage.setItem('storage_version', this.STORAGE_VERSION);
        AuthDebugger.log('STORAGE', 'Storage version updated');
      }
    } catch (error) {
      AuthDebugger.log('ERROR', 'Error checking storage version', { error: error.message });
    }
  },

  // Clear Supabase auth data specifically
  clearSupabaseAuth() {
    try {
      const keysToRemove = [];

      // Check localStorage
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('sb-'))) {
          keysToRemove.push(key);
        }
      }

      // Remove Supabase keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        AuthDebugger.log('STORAGE', 'Removed Supabase key', { key });
      });

      // Also clear sessionStorage
      sessionStorage.clear();

      AuthDebugger.log('STORAGE', 'Cleared Supabase auth data', { removedKeys: keysToRemove.length });
    } catch (error) {
      AuthDebugger.log('ERROR', 'Error clearing Supabase auth', { error: error.message });
    }
  },

  // Clear all storage (for debugging)
  clearAll() {
    console.log('🗑️ Clearing all storage...');
    localStorage.clear();
    sessionStorage.clear();
  }
};

// Make StorageManager available globally for debugging
if (typeof window !== 'undefined') {
  window.StorageManager = StorageManager;
  console.log('🔧 StorageManager available globally as window.StorageManager');
}

// Create the auth context
const AuthContext = createContext();

// Custom hook to use the auth context
export const useAuth = () => useContext(AuthContext);

// Auth provider component
export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [fallbackMode, setFallbackMode] = useState(false);
  const [profileLoading, setProfileLoading] = useState(false);
  const [profileCache, setProfileCache] = useState(new Map()); // Cache for user profiles

  // Refs to prevent duplicate operations
  const isLoadingProfile = useRef(false);
  const isInitializing = useRef(false);
  const authSubscriptionRef = useRef(null);

  // Load user profile with caching and performance optimization
  const loadUserProfile = async (userId, forceRefresh = false) => {
    const timer = AuthDebugger.startTimer(`loadUserProfile-${userId}`);

    try {
      // Prevent duplicate profile loading
      if (isLoadingProfile.current && !forceRefresh) {
        AuthDebugger.log('PROFILE', 'Profile loading already in progress, skipping', { userId });
        return;
      }

      // Check cache first (unless force refresh)
      if (!forceRefresh && profileCache.has(userId)) {
        const cachedProfile = profileCache.get(userId);
        AuthDebugger.log('PROFILE', 'Using cached profile', { userId, hasProfile: !!cachedProfile });
        setUserProfile(cachedProfile);
        timer.end();
        return cachedProfile;
      }

      // Prevent multiple simultaneous profile loads
      if (profileLoading && !forceRefresh) {
        AuthDebugger.log('PROFILE', 'Profile loading state active, skipping duplicate request', { userId });
        timer.end();
        return;
      }

      isLoadingProfile.current = true;
      setProfileLoading(true);
      AuthDebugger.log('PROFILE', 'Starting profile load', { userId, forceRefresh });

      const profile = await UserProfileService.getUserProfile(userId);

      // Cache the profile
      setProfileCache(prev => new Map(prev.set(userId, profile)));
      setUserProfile(profile);

      AuthDebugger.log('PROFILE', 'Profile loaded successfully', {
        userId,
        role: profile?.role,
        isAdmin: profile?.role === 'admin'
      });

      timer.end();
      return profile;
    } catch (error) {
      AuthDebugger.log('ERROR', 'Profile loading failed', { userId, error: error.message });
      // Set a fallback profile to prevent infinite loading
      setUserProfile(null);
      timer.end();
      return null;
    } finally {
      setProfileLoading(false);
      isLoadingProfile.current = false;
    }
  };

  // Check if user is admin
  const isAdmin = () => {
    const result = userProfile?.role === 'admin';
    console.log('🔍 isAdmin check:', {
      userProfile: userProfile,
      role: userProfile?.role,
      isAdmin: result
    });
    return result;
  };

  // Check if user has specific permission
  const hasPermission = (permission) => {
    if (!userProfile) return false;
    if (userProfile.role === 'admin') return true;
    return userProfile.permissions?.[permission] === true;
  };

  // Check for user session on initial load
  useEffect(() => {
    // Prevent multiple initializations
    if (isInitializing.current) {
      AuthDebugger.log('AUTH', 'Initialization already in progress, skipping');
      return;
    }

    isInitializing.current = true;
    AuthDebugger.log('AUTH', 'Starting authentication initialization');

    // Check storage version and clear conflicting data if needed
    StorageManager.checkStorageVersion();
    AuthDebugger.checkStorageHealth();

    let loadingTimeout;

    async function getInitialSession() {
      const timer = AuthDebugger.startTimer('getInitialSession');

      try {
        setLoading(true);
        AuthDebugger.log('AUTH', 'Getting initial session');

        // Set a timeout to prevent infinite loading
        loadingTimeout = setTimeout(() => {
          AuthDebugger.log('ERROR', 'Loading timeout reached, enabling fallback mode');
          setLoading(false);
          setFallbackMode(true);
          setUser({ id: 'fallback-user', email: '<EMAIL>', fallback: true });
        }, 15000); // Reduced to 15 seconds for faster fallback

        // Test Supabase connection first
        console.log('Testing Supabase connection...');
        console.log('Supabase URL:', supabase.supabaseUrl);
        console.log('Supabase Key (first 20 chars):', supabase.supabaseKey?.substring(0, 20) + '...');

        // Get current session
        console.log('Getting current session...');
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Session error:', sessionError);
          throw sessionError;
        }

        AuthDebugger.log('AUTH', 'Session check result', { hasSession: !!session });

        if (session) {
          const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();

          if (userError) {
            throw userError;
          }

          AuthDebugger.log('AUTH', 'User found in session', { userId: currentUser.id });
          setUser(currentUser);

          // Load user profile with timeout protection
          try {
            const profileTimer = AuthDebugger.startTimer('initialProfileLoad');
            await Promise.race([
              loadUserProfile(currentUser.id),
              new Promise((_, reject) =>
                setTimeout(() => reject(new Error('Profile load timeout')), 10000)
              )
            ]);
            profileTimer.end();
          } catch (profileError) {
            AuthDebugger.log('ERROR', 'Profile loading failed during initialization', {
              userId: currentUser.id,
              error: profileError.message
            });
            // Continue without profile to prevent infinite loading
          }
        } else {
          AuthDebugger.log('AUTH', 'No session found, clearing user data');
          // No session - clear any cached user data
          StorageManager.clearUserSpecificData();
        }
      } catch (err) {
        AuthDebugger.log('ERROR', 'Initial session error, enabling fallback mode', { error: err.message });
        setError(err.message);
        setFallbackMode(true);
        // In fallback mode, create a mock user so the app works
        setUser({ id: 'fallback-user', email: '<EMAIL>', fallback: true });
      } finally {
        if (loadingTimeout) clearTimeout(loadingTimeout); // Clear the timeout
        setLoading(false);
        isInitializing.current = false;
        timer.end();
        AuthDebugger.log('AUTH', 'Initial session check completed');
      }
    }

    getInitialSession();

    // Set up auth state change listener (only if not in fallback mode)
    if (!fallbackMode && !authSubscriptionRef.current) {
      try {
        AuthDebugger.log('AUTH', 'Setting up auth state listener');
        const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(
          async (event, session) => {
            AuthDebugger.log('AUTH', 'Auth state changed', { event, hasSession: !!session });

            if (session) {
              setUser(session.user);
              // Load user profile (but avoid duplicate loading)
              try {
                await loadUserProfile(session.user.id);
              } catch (profileError) {
                AuthDebugger.log('ERROR', 'Profile loading failed in auth listener', {
                  userId: session.user.id,
                  error: profileError.message
                });
                // Continue without profile
              }
            } else {
              // User signed out - clear everything
              AuthDebugger.log('AUTH', 'User signed out, clearing data');
              setUser(null);
              setUserProfile(null);
              setProfileCache(new Map()); // Clear profile cache
              StorageManager.clearUserSpecificData(); // Clear storage
            }

            setLoading(false);
          }
        );
        authSubscriptionRef.current = authSubscription;
      } catch (err) {
        AuthDebugger.log('ERROR', 'Error setting up auth listener', { error: err.message });
      }
    }
    
    // Clean up subscription on unmount
    return () => {
      if (authSubscriptionRef.current) {
        AuthDebugger.log('AUTH', 'Cleaning up auth subscription');
        authSubscriptionRef.current.unsubscribe();
        authSubscriptionRef.current = null;
      }
      isInitializing.current = false;
      isLoadingProfile.current = false;
    };
  }, []); // Empty dependency array to prevent re-running
  
  // Sign in with email and password
  const signIn = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error signing in:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Sign up with email and password
  const signUp = async (email, password) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error signing up:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Sign out
  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);

      // Clear local state and storage first
      setUser(null);
      setUserProfile(null);
      setProfileCache(new Map());
      StorageManager.clearUserSpecificData();

      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      console.log('✅ Successfully signed out and cleared data');
    } catch (err) {
      console.error('Error signing out:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  // Reset password
  const resetPassword = async (email) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`
      });
      
      if (error) {
        throw error;
      }
      
      return data;
    } catch (err) {
      console.error('Error resetting password:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Update user profile
  const updateProfile = async (updates) => {
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase.auth.updateUser(updates);
      
      if (error) {
        throw error;
      }
      
      setUser(data.user);
      return data;
    } catch (err) {
      console.error('Error updating profile:', err);
      setError(err.message);
      return { error: err };
    } finally {
      setLoading(false);
    }
  };
  
  // Context value
  const value = {
    user,
    userProfile,
    loading,
    error,
    fallbackMode,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updateProfile,
    setError,
    isAdmin,
    hasPermission,
    loadUserProfile,
    StorageManager // Expose storage manager for debugging
  };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
