import React, { useState, useRef, useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter as Router, Routes, Route, NavLink, Navigate, Link } from 'react-router-dom';
import QuestionnaireBuilder from './components/QuestionnaireBuilder';
import ResponseViewer from './components/ResponseViewer';
import '../style.css';

// Import new components
import Home from './components/Home';
import ClientAcquisition from './components/ClientAcquisition';
import CustomerRetention from './components/CustomerRetention';
import Tools from './components/Tools';
import MarketResearch from './components/MarketResearch';
import CompetitionAnalysis from './components/CompetitionAnalysis';
import Partnerships from './components/Partnerships';
import StrategyPage from './components/StrategyPage';
import ResponseView from './components/ResponseView';
import SupabaseTest from './components/SupabaseTest';
import SupabaseConnectionTest from './components/SupabaseConnectionTest';
import ResponseDebug from './components/ResponseDebug';
import PromptManagement from './components/PromptManagement';
import CompanyProfile from './components/CompanyProfile.jsx';
import AdminSettings from './components/AdminSettings';

// Import auth components
import Login from './components/Login';
import Signup from './components/Signup';
import ForgotPassword from './components/ForgotPassword';
import ResetPassword from './components/ResetPassword';
import UserProfile from './components/UserProfile';
import ProtectedRoute from './components/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';
import { AuthProvider, useAuth } from './context/AuthContext';

// Header component that only shows when user is logged in
function AppHeader() {
  const { user, userProfile, signOut } = useAuth();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setDropdownOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [dropdownRef]);
  
  return (
    <header className="bg-white shadow-sm">
      <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8 flex flex-col items-center text-center">
        <div className="w-full flex justify-end mb-2">
          <div className="px-4 py-2 bg-gray-100 rounded-lg text-gray-700 text-sm mr-2">
            <span className="font-medium"></span> {user.email}
          </div>
          <div className="flex space-x-2">
            <div className="relative" ref={dropdownRef}>
              <button 
                className="px-4 py-2 bg-gray-100 rounded-lg text-gray-700 text-sm hover:bg-gray-200 transition-colors flex items-center"
                onClick={() => setDropdownOpen(!dropdownOpen)}
              >
                Profile
                <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d={dropdownOpen ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"} />
                </svg>
              </button>
              
              {dropdownOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10">
                  <div className="py-1">
                    <Link 
                      to="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setDropdownOpen(false)}
                    >
                      User Profile
                    </Link>
                    <Link
                      to="/company-profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setDropdownOpen(false)}
                    >
                      Company Profile
                    </Link>
                    {(userProfile?.role === 'admin') && (
                      <Link
                        to="/admin"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setDropdownOpen(false)}
                      >
                        Admin Settings
                      </Link>
                    )}
                  </div>
                </div>
              )}
            </div>
            <button 
              className="px-4 py-2 bg-gray-100 rounded-lg text-gray-700 text-sm hover:bg-gray-200 transition-colors"
              onClick={signOut}
            >
              Logout
            </button>
          </div>
        </div>
        {/* Logo */}
        <div className="flex justify-center mb-6">
          <img
            src="/op-logo-100px.png"
            alt="Omega Praxis Logo"
            className="h-[100px] w-[100px]"
          />
        </div>

        <h1 className="px-4 py-5 font-light raleway-title-h3-wide">
          Market Intelligence
          </h1>

        <nav className="mt-2">
          <ul className="flex mb-6">
            <li>
              <NavLink to="/" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
                end
              >
                Home
              </NavLink>
            </li>
            <li>
              <NavLink to="/market-research" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Research
              </NavLink>
            </li>
            <li>
              <NavLink to="/competition-analysis"
                className={({ isActive }) =>
                  `py-2 px-4 mr-4 raleway-menu ${isActive
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Analysis
              </NavLink>
            </li>
            <li>
              <NavLink to="/client-acquisition" 
                className={({ isActive }) => 
                  `py-2 px-4 mr-4 raleway-menu ${isActive 
                    ? 'border-b-2 border-blue-500 text-blue-600' 
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Acquisition
              </NavLink>
            </li>
            <li>
              <NavLink to="/customer-retention"
                className={({ isActive }) =>
                  `py-2 px-4 mr-4 raleway-menu ${isActive
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Retention
              </NavLink>
            </li>
            <li>
              <NavLink to="/partnerships"
                className={({ isActive }) =>
                  `py-2 px-4 mr-4 raleway-menu ${isActive
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Partnerships
              </NavLink>
            </li>

            <li>
              <NavLink to="/prompts"
                className={({ isActive }) =>
                  `py-2 px-4 mr-4 raleway-menu ${isActive
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-blue-400'}`
                }
              >
                Prompts
              </NavLink>
            </li>
            {(user && userProfile?.role === 'admin') && (
              <li>
                <NavLink to="/admin"
                  className={({ isActive }) =>
                    `py-2 px-4 mr-4 raleway-menu ${isActive
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-500 hover:text-blue-400'}`
                  }
                >
                  Admin
                </NavLink>
              </li>
            )}
          </ul>
        </nav>
      </div>
    </header>
  );
}

// Main App component with conditional header
function App() {
  const { user, userProfile, loading, fallbackMode } = useAuth();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Show fallback mode notice */}
      {fallbackMode && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
          <div className="flex justify-between items-center">
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Running in offline mode. Some features may be limited.
              </p>
            </div>
            <div className="flex space-x-2">
              <Link
                to="/connection-test"
                className="text-sm bg-red-100 hover:bg-red-200 text-red-800 px-3 py-1 rounded"
              >
                Diagnose
              </Link>
              <button
                onClick={() => window.location.reload()}
                className="text-sm bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-3 py-1 rounded"
              >
                Reconnect
              </button>
              <Link
                to="/login"
                className="text-sm bg-blue-100 hover:bg-blue-200 text-blue-800 px-3 py-1 rounded"
              >
                Login
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* Only show header when user is logged in */}
      {user && <AppHeader />}
      
      <main className="flex justify-center items-center">
        <div className="max-w-7xl w-full mx-auto py-6 sm:px-6 lg:px-8 flex justify-center">
          <div className="w-full max-w-4xl">
            <Routes>
              {/* Public routes - no header shown */}
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<Signup />} />
              <Route path="/forgot-password" element={<ForgotPassword />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route path="/supabase-test" element={<SupabaseTest />} />
              <Route path="/connection-test" element={<SupabaseConnectionTest />} />
              <Route path="/response-debug" element={<ResponseDebug />} />
              
              {/* Protected routes - header shown */}
              <Route element={<ProtectedRoute />}>
                <Route path="/" element={<Home />} />
                <Route path="/client-acquisition" element={<ClientAcquisition />} />
                <Route path="/customer-retention" element={<CustomerRetention />} />
                <Route path="/partnerships" element={<Partnerships />} />
                <Route path="/market-research" element={<MarketResearch />} />
                <Route path="/competition-analysis" element={<CompetitionAnalysis />} />
                <Route path="/tools" element={<Tools />} />
                <Route path="/strategy" element={<StrategyPage />} />
                <Route path="/responses" element={<ResponseView />} />
                <Route path="/profile" element={<UserProfile />} />
                <Route path="/company-profile" element={<CompanyProfile />} />
                <Route path="/prompts" element={<PromptManagement />} />
                <Route path="/admin" element={<AdminSettings />} />
              </Route>
              
              {/* Fallback route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </div>
      </main>
    </div>
  );
}

// Render the app
const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <ErrorBoundary>
      <Router>
        <AuthProvider>
          <App />
        </AuthProvider>
      </Router>
    </ErrorBoundary>
  </React.StrictMode>
);
